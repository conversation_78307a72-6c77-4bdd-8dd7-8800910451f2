﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{44CF59C0-0333-3851-8724-46D75AAFAB0A}"
	ProjectSection(ProjectDependencies) = postProject
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4} = {0F8511A6-FFCB-30D5-B5BF-7300354600F4}
		{152CCAD2-C788-3CB2-A145-DA4A59CD18E5} = {152CCAD2-C788-3CB2-A145-DA4A59CD18E5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{B6F0443D-6628-34EF-A764-D905743F47C1}"
	ProjectSection(ProjectDependencies) = postProject
		{44CF59C0-0333-3851-8724-46D75AAFAB0A} = {44CF59C0-0333-3851-8724-46D75AAFAB0A}
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4} = {0F8511A6-FFCB-30D5-B5BF-7300354600F4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{0F8511A6-FFCB-30D5-B5BF-7300354600F4}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\flutter\flutter_assemble.vcxproj", "{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}"
	ProjectSection(ProjectDependencies) = postProject
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4} = {0F8511A6-FFCB-30D5-B5BF-7300354600F4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "..\flutter\flutter_wrapper_app.vcxproj", "{9A63069B-8589-3339-9665-FEF4FEB179F1}"
	ProjectSection(ProjectDependencies) = postProject
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4} = {0F8511A6-FFCB-30D5-B5BF-7300354600F4}
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09} = {3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "my_app", "my_app.vcxproj", "{152CCAD2-C788-3CB2-A145-DA4A59CD18E5}"
	ProjectSection(ProjectDependencies) = postProject
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4} = {0F8511A6-FFCB-30D5-B5BF-7300354600F4}
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09} = {3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}
		{9A63069B-8589-3339-9665-FEF4FEB179F1} = {9A63069B-8589-3339-9665-FEF4FEB179F1}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{44CF59C0-0333-3851-8724-46D75AAFAB0A}.Debug|x64.ActiveCfg = Debug|x64
		{44CF59C0-0333-3851-8724-46D75AAFAB0A}.Debug|x64.Build.0 = Debug|x64
		{44CF59C0-0333-3851-8724-46D75AAFAB0A}.Profile|x64.ActiveCfg = Profile|x64
		{44CF59C0-0333-3851-8724-46D75AAFAB0A}.Profile|x64.Build.0 = Profile|x64
		{44CF59C0-0333-3851-8724-46D75AAFAB0A}.Release|x64.ActiveCfg = Release|x64
		{44CF59C0-0333-3851-8724-46D75AAFAB0A}.Release|x64.Build.0 = Release|x64
		{B6F0443D-6628-34EF-A764-D905743F47C1}.Debug|x64.ActiveCfg = Debug|x64
		{B6F0443D-6628-34EF-A764-D905743F47C1}.Profile|x64.ActiveCfg = Profile|x64
		{B6F0443D-6628-34EF-A764-D905743F47C1}.Release|x64.ActiveCfg = Release|x64
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4}.Debug|x64.ActiveCfg = Debug|x64
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4}.Debug|x64.Build.0 = Debug|x64
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4}.Profile|x64.ActiveCfg = Profile|x64
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4}.Profile|x64.Build.0 = Profile|x64
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4}.Release|x64.ActiveCfg = Release|x64
		{0F8511A6-FFCB-30D5-B5BF-7300354600F4}.Release|x64.Build.0 = Release|x64
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}.Debug|x64.ActiveCfg = Debug|x64
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}.Debug|x64.Build.0 = Debug|x64
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}.Profile|x64.ActiveCfg = Profile|x64
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}.Profile|x64.Build.0 = Profile|x64
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}.Release|x64.ActiveCfg = Release|x64
		{3CF36A95-AAD0-3E8B-A410-2C930E5AAF09}.Release|x64.Build.0 = Release|x64
		{9A63069B-8589-3339-9665-FEF4FEB179F1}.Debug|x64.ActiveCfg = Debug|x64
		{9A63069B-8589-3339-9665-FEF4FEB179F1}.Debug|x64.Build.0 = Debug|x64
		{9A63069B-8589-3339-9665-FEF4FEB179F1}.Profile|x64.ActiveCfg = Profile|x64
		{9A63069B-8589-3339-9665-FEF4FEB179F1}.Profile|x64.Build.0 = Profile|x64
		{9A63069B-8589-3339-9665-FEF4FEB179F1}.Release|x64.ActiveCfg = Release|x64
		{9A63069B-8589-3339-9665-FEF4FEB179F1}.Release|x64.Build.0 = Release|x64
		{152CCAD2-C788-3CB2-A145-DA4A59CD18E5}.Debug|x64.ActiveCfg = Debug|x64
		{152CCAD2-C788-3CB2-A145-DA4A59CD18E5}.Debug|x64.Build.0 = Debug|x64
		{152CCAD2-C788-3CB2-A145-DA4A59CD18E5}.Profile|x64.ActiveCfg = Profile|x64
		{152CCAD2-C788-3CB2-A145-DA4A59CD18E5}.Profile|x64.Build.0 = Profile|x64
		{152CCAD2-C788-3CB2-A145-DA4A59CD18E5}.Release|x64.ActiveCfg = Release|x64
		{152CCAD2-C788-3CB2-A145-DA4A59CD18E5}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2AD5F142-B85B-359E-B245-DD217B49D21A}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
