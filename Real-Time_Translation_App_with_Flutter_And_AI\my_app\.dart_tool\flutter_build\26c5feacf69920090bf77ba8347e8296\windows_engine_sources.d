 E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_windows.dll E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_export.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_messenger.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\flutter_windows.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\icudtl.dat E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h:  E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc E:\\Flutter\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h