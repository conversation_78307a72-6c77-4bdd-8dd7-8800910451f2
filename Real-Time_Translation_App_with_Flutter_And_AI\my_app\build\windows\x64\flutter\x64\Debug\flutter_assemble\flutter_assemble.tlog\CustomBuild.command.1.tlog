^E:\FLUTTER_APPS\REAL-TIME_TRANSLATION_APP_WITH_FLUTTER_AND_AI\MY_APP\BUILD\WINDOWS\X64\CMAKEFILES\ADFA7EB8311A0B66AE44FA0FDD7A331A\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=E:\Flutter\flutter PROJECT_DIR=E:\FLUTTER_APPS\Real-Time_Translation_App_with_Flutter_And_AI\my_app FLUTTER_ROOT=E:\Flutter\flutter FLUTTER_EPHEMERAL_DIR=E:\FLUTTER_APPS\Real-Time_Translation_App_with_Flutter_And_AI\my_app\windows\flutter\ephemeral PROJECT_DIR=E:\FLUTTER_APPS\Real-Time_Translation_App_with_Flutter_And_AI\my_app FLUTTER_TARGET=E:\FLUTTER_APPS\Real-Time_Translation_App_with_Flutter_And_AI\my_app\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=E:\FLUTTER_APPS\Real-Time_Translation_App_with_Flutter_And_AI\my_app\.dart_tool\package_config.json E:/Flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\FLUTTER_APPS\REAL-TIME_TRANSLATION_APP_WITH_FLUTTER_AND_AI\MY_APP\BUILD\WINDOWS\X64\CMAKEFILES\3BB3B510484222946E47ED401CB0E222\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\FLUTTER_APPS\REAL-TIME_TRANSLATION_APP_WITH_FLUTTER_AND_AI\MY_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/windows -BE:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/build/windows/x64 --check-stamp-file E:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
