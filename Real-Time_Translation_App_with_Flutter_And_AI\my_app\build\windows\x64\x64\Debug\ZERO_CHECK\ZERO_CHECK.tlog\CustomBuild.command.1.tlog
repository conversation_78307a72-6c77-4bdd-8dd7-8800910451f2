^E:\FLUTTER_APPS\REAL-TIME_TRANSLATION_APP_WITH_FLUTTER_AND_AI\MY_APP\BUILD\WINDOWS\X64\CMAKEFILES\AFEAEDF5DA480E7FDBA9BD11DAAAA24D\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/windows -BE:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/build/windows/x64/my_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
