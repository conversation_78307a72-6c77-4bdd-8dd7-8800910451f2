int anim fragment_fast_out_extra_slow_in 0x7f010000
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int attr activityAction 0x7f030000
int attr activityName 0x7f030001
int attr alpha 0x7f030002
int attr alwaysExpand 0x7f030003
int attr animationBackgroundColor 0x7f030004
int attr clearTop 0x7f030005
int attr finishPrimaryWithPlaceholder 0x7f030006
int attr finishPrimaryWithSecondary 0x7f030007
int attr finishSecondaryWithPrimary 0x7f030008
int attr font 0x7f030009
int attr fontProviderAuthority 0x7f03000a
int attr fontProviderCerts 0x7f03000b
int attr fontProviderFetchStrategy 0x7f03000c
int attr fontProviderFetchTimeout 0x7f03000d
int attr fontProviderPackage 0x7f03000e
int attr fontProviderQuery 0x7f03000f
int attr fontProviderSystemFontFamily 0x7f030010
int attr fontStyle 0x7f030011
int attr fontVariationSettings 0x7f030012
int attr fontWeight 0x7f030013
int attr lStar 0x7f030014
int attr nestedScrollViewStyle 0x7f030015
int attr placeholderActivityName 0x7f030016
int attr primaryActivityName 0x7f030017
int attr queryPatterns 0x7f030018
int attr secondaryActivityAction 0x7f030019
int attr secondaryActivityName 0x7f03001a
int attr shortcutMatchRequired 0x7f03001b
int attr splitLayoutDirection 0x7f03001c
int attr splitMaxAspectRatioInLandscape 0x7f03001d
int attr splitMaxAspectRatioInPortrait 0x7f03001e
int attr splitMinHeightDp 0x7f03001f
int attr splitMinSmallestWidthDp 0x7f030020
int attr splitMinWidthDp 0x7f030021
int attr splitRatio 0x7f030022
int attr stickyPlaceholder 0x7f030023
int attr tag 0x7f030024
int attr ttcIndex 0x7f030025
int color androidx_core_ripple_material_light 0x7f040000
int color androidx_core_secondary_text_default_material_light 0x7f040001
int color call_notification_answer_color 0x7f040002
int color call_notification_decline_color 0x7f040003
int color notification_action_color_filter 0x7f040004
int color notification_icon_bg_color 0x7f040005
int dimen compat_button_inset_horizontal_material 0x7f050000
int dimen compat_button_inset_vertical_material 0x7f050001
int dimen compat_button_padding_horizontal_material 0x7f050002
int dimen compat_button_padding_vertical_material 0x7f050003
int dimen compat_control_corner_material 0x7f050004
int dimen compat_notification_large_icon_max_height 0x7f050005
int dimen compat_notification_large_icon_max_width 0x7f050006
int dimen notification_action_icon_size 0x7f050007
int dimen notification_action_text_size 0x7f050008
int dimen notification_big_circle_margin 0x7f050009
int dimen notification_content_margin_start 0x7f05000a
int dimen notification_large_icon_height 0x7f05000b
int dimen notification_large_icon_width 0x7f05000c
int dimen notification_main_column_padding_top 0x7f05000d
int dimen notification_media_narrow_margin 0x7f05000e
int dimen notification_right_icon_size 0x7f05000f
int dimen notification_right_side_padding_top 0x7f050010
int dimen notification_small_icon_background_padding 0x7f050011
int dimen notification_small_icon_size_as_large 0x7f050012
int dimen notification_subtext_size 0x7f050013
int dimen notification_top_pad 0x7f050014
int dimen notification_top_pad_large_text 0x7f050015
int drawable ic_call_answer 0x7f060000
int drawable ic_call_answer_low 0x7f060001
int drawable ic_call_answer_video 0x7f060002
int drawable ic_call_answer_video_low 0x7f060003
int drawable ic_call_decline 0x7f060004
int drawable ic_call_decline_low 0x7f060005
int drawable launch_background 0x7f060006
int drawable notification_action_background 0x7f060007
int drawable notification_bg 0x7f060008
int drawable notification_bg_low 0x7f060009
int drawable notification_bg_low_normal 0x7f06000a
int drawable notification_bg_low_pressed 0x7f06000b
int drawable notification_bg_normal 0x7f06000c
int drawable notification_bg_normal_pressed 0x7f06000d
int drawable notification_icon_background 0x7f06000e
int drawable notification_oversize_large_icon_bg 0x7f06000f
int drawable notification_template_icon_bg 0x7f060010
int drawable notification_template_icon_low_bg 0x7f060011
int drawable notification_tile_bg 0x7f060012
int drawable notify_panel_notification_icon_bg 0x7f060013
int id accessibility_action_clickable_span 0x7f070000
int id accessibility_custom_action_0 0x7f070001
int id accessibility_custom_action_1 0x7f070002
int id accessibility_custom_action_10 0x7f070003
int id accessibility_custom_action_11 0x7f070004
int id accessibility_custom_action_12 0x7f070005
int id accessibility_custom_action_13 0x7f070006
int id accessibility_custom_action_14 0x7f070007
int id accessibility_custom_action_15 0x7f070008
int id accessibility_custom_action_16 0x7f070009
int id accessibility_custom_action_17 0x7f07000a
int id accessibility_custom_action_18 0x7f07000b
int id accessibility_custom_action_19 0x7f07000c
int id accessibility_custom_action_2 0x7f07000d
int id accessibility_custom_action_20 0x7f07000e
int id accessibility_custom_action_21 0x7f07000f
int id accessibility_custom_action_22 0x7f070010
int id accessibility_custom_action_23 0x7f070011
int id accessibility_custom_action_24 0x7f070012
int id accessibility_custom_action_25 0x7f070013
int id accessibility_custom_action_26 0x7f070014
int id accessibility_custom_action_27 0x7f070015
int id accessibility_custom_action_28 0x7f070016
int id accessibility_custom_action_29 0x7f070017
int id accessibility_custom_action_3 0x7f070018
int id accessibility_custom_action_30 0x7f070019
int id accessibility_custom_action_31 0x7f07001a
int id accessibility_custom_action_4 0x7f07001b
int id accessibility_custom_action_5 0x7f07001c
int id accessibility_custom_action_6 0x7f07001d
int id accessibility_custom_action_7 0x7f07001e
int id accessibility_custom_action_8 0x7f07001f
int id accessibility_custom_action_9 0x7f070020
int id action_container 0x7f070021
int id action_divider 0x7f070022
int id action_image 0x7f070023
int id action_text 0x7f070024
int id actions 0x7f070025
int id adjacent 0x7f070026
int id always 0x7f070027
int id alwaysAllow 0x7f070028
int id alwaysDisallow 0x7f070029
int id androidx_window_activity_scope 0x7f07002a
int id async 0x7f07002b
int id blocking 0x7f07002c
int id bottomToTop 0x7f07002d
int id chronometer 0x7f07002e
int id dialog_button 0x7f07002f
int id edit_text_id 0x7f070030
int id forever 0x7f070031
int id fragment_container_view_tag 0x7f070032
int id hide_ime_id 0x7f070033
int id icon 0x7f070034
int id icon_group 0x7f070035
int id info 0x7f070036
int id italic 0x7f070037
int id line1 0x7f070038
int id line3 0x7f070039
int id locale 0x7f07003a
int id ltr 0x7f07003b
int id never 0x7f07003c
int id normal 0x7f07003d
int id notification_background 0x7f07003e
int id notification_main_column 0x7f07003f
int id notification_main_column_container 0x7f070040
int id report_drawn 0x7f070041
int id right_icon 0x7f070042
int id right_side 0x7f070043
int id rtl 0x7f070044
int id special_effects_controller_view_tag 0x7f070045
int id tag_accessibility_actions 0x7f070046
int id tag_accessibility_clickable_spans 0x7f070047
int id tag_accessibility_heading 0x7f070048
int id tag_accessibility_pane_title 0x7f070049
int id tag_on_apply_window_listener 0x7f07004a
int id tag_on_receive_content_listener 0x7f07004b
int id tag_on_receive_content_mime_types 0x7f07004c
int id tag_screen_reader_focusable 0x7f07004d
int id tag_state_description 0x7f07004e
int id tag_transition_group 0x7f07004f
int id tag_unhandled_key_event_manager 0x7f070050
int id tag_unhandled_key_listeners 0x7f070051
int id tag_window_insets_animation_callback 0x7f070052
int id text 0x7f070053
int id text2 0x7f070054
int id time 0x7f070055
int id title 0x7f070056
int id topToBottom 0x7f070057
int id view_tree_lifecycle_owner 0x7f070058
int id view_tree_on_back_pressed_dispatcher_owner 0x7f070059
int id view_tree_saved_state_registry_owner 0x7f07005a
int id view_tree_view_model_store_owner 0x7f07005b
int id visible_removing_fragment_view_tag 0x7f07005c
int integer status_bar_notification_info_maxnum 0x7f080000
int layout custom_dialog 0x7f090000
int layout ime_base_split_test_activity 0x7f090001
int layout ime_secondary_split_test_activity 0x7f090002
int layout notification_action 0x7f090003
int layout notification_action_tombstone 0x7f090004
int layout notification_template_custom_big 0x7f090005
int layout notification_template_icon_group 0x7f090006
int layout notification_template_part_chronometer 0x7f090007
int layout notification_template_part_time 0x7f090008
int mipmap ic_launcher 0x7f0a0000
int string androidx_startup 0x7f0b0000
int string call_notification_answer_action 0x7f0b0001
int string call_notification_answer_video_action 0x7f0b0002
int string call_notification_decline_action 0x7f0b0003
int string call_notification_hang_up_action 0x7f0b0004
int string call_notification_incoming_text 0x7f0b0005
int string call_notification_ongoing_text 0x7f0b0006
int string call_notification_screening_text 0x7f0b0007
int string status_bar_notification_info_overflow 0x7f0b0008
int style LaunchTheme 0x7f0c0000
int style NormalTheme 0x7f0c0001
int style TextAppearance_Compat_Notification 0x7f0c0002
int style TextAppearance_Compat_Notification_Info 0x7f0c0003
int style TextAppearance_Compat_Notification_Line2 0x7f0c0004
int style TextAppearance_Compat_Notification_Time 0x7f0c0005
int style TextAppearance_Compat_Notification_Title 0x7f0c0006
int style Widget_Compat_NotificationActionContainer 0x7f0c0007
int style Widget_Compat_NotificationActionText 0x7f0c0008
int[] styleable ActivityFilter { 0x7f030000, 0x7f030001 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x7f030003, 0x7f030024 }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable Capability { 0x7f030018, 0x7f03001b }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030002, 0x7f030014 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030010 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030009, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030025 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable SplitPairFilter { 0x7f030017, 0x7f030019, 0x7f03001a }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f030004, 0x7f030005, 0x7f030007, 0x7f030008, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030024 }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x7f030004, 0x7f030006, 0x7f030016, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030024 }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
