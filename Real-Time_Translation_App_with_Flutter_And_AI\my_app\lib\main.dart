import 'package:flutter/material.dart';
import 'package:google_mlkit_translation/google_mlkit_translation.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController _textController = TextEditingController();

  OnDeviceTranslator? onDeviceTranslator;
  final modelManager = OnDeviceTranslatorModelManager();
  bool isTranslating = false;
  String translatedText = '';

  // Language selection variables
  TranslateLanguage? selectedInputLanguage;
  TranslateLanguage? selectedOutputLanguage;

  // Available languages
  final Map<String, TranslateLanguage> languages = {
    'English': TranslateLanguage.english,
    'Spanish': TranslateLanguage.spanish,
    'French': TranslateLanguage.french,
    'German': TranslateLanguage.german,
    'Italian': TranslateLanguage.italian,
    'Portuguese': TranslateLanguage.portuguese,
    'Russian': TranslateLanguage.russian,
    'Chinese': TranslateLanguage.chinese,
    'Japanese': TranslateLanguage.japanese,
    'Korean': TranslateLanguage.korean,
    'Arabic': TranslateLanguage.arabic,
    'Hindi': TranslateLanguage.hindi,
  };

  @override
  void initState() {
    super.initState();
  }

  Future<void> _initializeTranslator() async {
    if (selectedInputLanguage == null || selectedOutputLanguage == null) {
      return;
    }

    // Close existing translator if any
    if (onDeviceTranslator != null) {
      await onDeviceTranslator!.close();
    }

    onDeviceTranslator = OnDeviceTranslator(
      sourceLanguage: selectedInputLanguage!,
      targetLanguage: selectedOutputLanguage!,
    );

    // Check and download models if needed
    await _downloadModelsIfNeeded();
  }

  Future<void> _downloadModelsIfNeeded() async {
    if (selectedInputLanguage == null || selectedOutputLanguage == null) {
      return;
    }

    final bool inputModelExists =
        await modelManager.isModelDownloaded(selectedInputLanguage!.bcpCode);
    final bool outputModelExists =
        await modelManager.isModelDownloaded(selectedOutputLanguage!.bcpCode);

    if (!inputModelExists) {
      await modelManager.downloadModel(selectedInputLanguage!.bcpCode);
    }
    if (!outputModelExists) {
      await modelManager.downloadModel(selectedOutputLanguage!.bcpCode);
    }
  }

  Future<void> _translateText() async {
    // Check if languages are selected
    if (selectedInputLanguage == null || selectedOutputLanguage == null) {
      _showLanguageNotSelectedDialog();
      return;
    }

    // Check if text is entered
    if (_textController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter text to translate'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      isTranslating = true;
      translatedText = '';
    });

    try {
      await _initializeTranslator();

      if (onDeviceTranslator != null) {
        final result =
            await onDeviceTranslator!.translateText(_textController.text);
        setState(() {
          translatedText = result;
          isTranslating = false;
        });
      }
    } catch (e) {
      setState(() {
        isTranslating = false;
        translatedText = 'Translation failed: ${e.toString()}';
      });
    }
  }

  void _showLanguageNotSelectedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Languages Not Selected'),
          content: const Text(
            'Please select both input and output languages before translating.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    onDeviceTranslator?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Real-Time Translation App',
            style: TextStyle(color: Colors.white)),
        centerTitle: true,
        backgroundColor: Colors.black,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 20),

              // Input Language Dropdown
              const Text(
                'Select Input Language:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<TranslateLanguage>(
                    isExpanded: true,
                    hint: const Text('Choose input language'),
                    value: selectedInputLanguage,
                    onChanged: (TranslateLanguage? newValue) {
                      setState(() {
                        selectedInputLanguage = newValue;
                        translatedText = ''; // Clear previous translation
                      });
                    },
                    items: languages.entries.map((entry) {
                      return DropdownMenuItem<TranslateLanguage>(
                        value: entry.value,
                        child: Text(entry.key),
                      );
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Output Language Dropdown
              const Text(
                'Select Output Language:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<TranslateLanguage>(
                    isExpanded: true,
                    hint: const Text('Choose output language'),
                    value: selectedOutputLanguage,
                    onChanged: (TranslateLanguage? newValue) {
                      setState(() {
                        selectedOutputLanguage = newValue;
                        translatedText = ''; // Clear previous translation
                      });
                    },
                    items: languages.entries.map((entry) {
                      return DropdownMenuItem<TranslateLanguage>(
                        value: entry.value,
                        child: Text(entry.key),
                      );
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 30),

              // Text input field
              TextField(
                controller: _textController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Enter text to translate',
                  hintText: 'Type your text here...',
                ),
                maxLines: 4,
                textInputAction: TextInputAction.done,
              ),

              const SizedBox(height: 20),

              // Translate button
              ElevatedButton(
                onPressed: isTranslating ? null : _translateText,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  textStyle: const TextStyle(fontSize: 18),
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                ),
                child: isTranslating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 10),
                          Text('Translating...'),
                        ],
                      )
                    : const Text('Translate'),
              ),

              const SizedBox(height: 30),

              // Translation result
              if (translatedText.isNotEmpty) ...[
                const Text(
                  'Translation:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    translatedText,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
