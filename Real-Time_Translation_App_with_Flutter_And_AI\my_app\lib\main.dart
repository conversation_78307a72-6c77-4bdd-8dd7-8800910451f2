import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController _textController = TextEditingController();

  // OpenAI API configuration
  static const String _openAiApiKey =
      'YOUR_OPENAI_API_KEY_HERE'; // Replace with your actual API key
  static const String _openAiApiUrl =
      'https://api.openai.com/v1/chat/completions';

  bool isTranslating = false;
  String translatedText = '';

  // Language selection variables
  String? selectedInputLanguage;
  String? selectedOutputLanguage;

  // Available languages
  final Map<String, String> languages = {
    'English': 'English',
    'Spanish': 'Spanish',
    'French': 'French',
    'German': 'German',
    'Italian': 'Italian',
    'Portuguese': 'Portuguese',
    'Russian': 'Russian',
    'Chinese (Simplified)': 'Chinese (Simplified)',
    'Chinese (Traditional)': 'Chinese (Traditional)',
    'Japanese': 'Japanese',
    'Korean': 'Korean',
    'Arabic': 'Arabic',
    'Hindi': 'Hindi',
    'Dutch': 'Dutch',
    'Swedish': 'Swedish',
    'Norwegian': 'Norwegian',
    'Danish': 'Danish',
    'Finnish': 'Finnish',
    'Polish': 'Polish',
    'Czech': 'Czech',
    'Hungarian': 'Hungarian',
    'Romanian': 'Romanian',
    'Bulgarian': 'Bulgarian',
    'Croatian': 'Croatian',
    'Serbian': 'Serbian',
    'Slovak': 'Slovak',
    'Slovenian': 'Slovenian',
    'Estonian': 'Estonian',
    'Latvian': 'Latvian',
    'Lithuanian': 'Lithuanian',
    'Greek': 'Greek',
    'Turkish': 'Turkish',
    'Hebrew': 'Hebrew',
    'Thai': 'Thai',
    'Vietnamese': 'Vietnamese',
    'Indonesian': 'Indonesian',
    'Malay': 'Malay',
    'Filipino': 'Filipino',
    'Swahili': 'Swahili',
    'Urdu': 'Urdu',
    'Bengali': 'Bengali',
    'Tamil': 'Tamil',
    'Telugu': 'Telugu',
    'Marathi': 'Marathi',
    'Gujarati': 'Gujarati',
    'Kannada': 'Kannada',
    'Malayalam': 'Malayalam',
    'Punjabi': 'Punjabi',
  };

  @override
  void initState() {
    super.initState();
  }

  Future<void> _translateText() async {
    // Check if languages are selected
    if (selectedInputLanguage == null || selectedOutputLanguage == null) {
      _showLanguageNotSelectedDialog();
      return;
    }

    // Check if text is entered
    if (_textController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter text to translate'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      isTranslating = true;
      translatedText = '';
    });

    try {
      final result = await _callOpenAITranslation(
        _textController.text,
        selectedInputLanguage!,
        selectedOutputLanguage!,
      );

      setState(() {
        translatedText = result;
        isTranslating = false;
      });
    } catch (e) {
      setState(() {
        isTranslating = false;
        translatedText = 'Translation failed: ${e.toString()}';
      });
    }
  }

  Future<String> _callOpenAITranslation(
      String text, String fromLang, String toLang) async {
    if (_openAiApiKey == 'YOUR_OPENAI_API_KEY_HERE') {
      throw Exception('Please set your OpenAI API key in the code');
    }

    final prompt = '''
You are a professional translator with expertise in multiple languages. Your task is to provide accurate, natural, and contextually appropriate translations.

Instructions:
- Translate the following text from $fromLang to $toLang
- Maintain the original meaning and tone
- Preserve any formatting, punctuation, and special characters
- If the text contains idioms or cultural references, provide the most appropriate equivalent in the target language
- If the text is already in the target language, return it as is
- Only return the translated text, no explanations or additional comments

Text to translate: "$text"

Translation:''';

    final requestBody = {
      'model': 'gpt-3.5-turbo',
      'messages': [
        {
          'role': 'system',
          'content':
              'You are a professional translator. Provide only the translated text without any explanations or additional comments.'
        },
        {'role': 'user', 'content': prompt}
      ],
      'max_tokens': 1000,
      'temperature': 0.3,
    };

    final response = await http.post(
      Uri.parse(_openAiApiUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_openAiApiKey',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      final translation =
          responseData['choices'][0]['message']['content'].toString().trim();
      return translation;
    } else {
      final errorData = json.decode(response.body);
      throw Exception('OpenAI API Error: ${errorData['error']['message']}');
    }
  }

  void _showLanguageNotSelectedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Languages Not Selected'),
          content: const Text(
            'Please select both input and output languages before translating.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Real-Time Translation App',
            style: TextStyle(color: Colors.white)),
        centerTitle: true,
        backgroundColor: Colors.black,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 20),

              // Input Language Dropdown
              const Text(
                'Select Input Language:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    isExpanded: true,
                    hint: const Text('Choose input language'),
                    value: selectedInputLanguage,
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedInputLanguage = newValue;
                        translatedText = ''; // Clear previous translation
                      });
                    },
                    items: languages.entries.map((entry) {
                      return DropdownMenuItem<String>(
                        value: entry.value,
                        child: Text(entry.key),
                      );
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Output Language Dropdown
              const Text(
                'Select Output Language:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    isExpanded: true,
                    hint: const Text('Choose output language'),
                    value: selectedOutputLanguage,
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedOutputLanguage = newValue;
                        translatedText = ''; // Clear previous translation
                      });
                    },
                    items: languages.entries.map((entry) {
                      return DropdownMenuItem<String>(
                        value: entry.value,
                        child: Text(entry.key),
                      );
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 30),

              // Text input field
              TextField(
                controller: _textController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Enter text to translate',
                  hintText: 'Type your text here...',
                ),
                maxLines: 4,
                textInputAction: TextInputAction.done,
              ),

              const SizedBox(height: 20),

              // Translate button
              ElevatedButton(
                onPressed: isTranslating ? null : _translateText,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  textStyle: const TextStyle(fontSize: 18),
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                ),
                child: isTranslating
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 10),
                          Text('Translating...'),
                        ],
                      )
                    : const Text('Translate'),
              ),

              const SizedBox(height: 30),

              // Translation result
              if (translatedText.isNotEmpty) ...[
                const Text(
                  'Translation:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    translatedText,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
