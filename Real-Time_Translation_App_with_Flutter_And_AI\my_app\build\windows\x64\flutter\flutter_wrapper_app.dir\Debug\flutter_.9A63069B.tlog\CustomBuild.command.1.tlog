^E:\FLUTTER_APPS\REAL-TIME_TRANSLATION_APP_WITH_FLUTTER_AND_AI\MY_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/windows -BE:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/build/windows/x64 --check-stamp-file E:/FLUTTER_APPS/Real-Time_Translation_App_with_Flutter_And_AI/my_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
