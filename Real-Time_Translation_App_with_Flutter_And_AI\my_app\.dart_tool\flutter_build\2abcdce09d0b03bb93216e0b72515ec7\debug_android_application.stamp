{"inputs": ["E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\.dart_tool\\flutter_build\\2abcdce09d0b03bb93216e0b72515ec7\\app.dill", "E:\\Flutter\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "E:\\Flutter\\flutter\\bin\\internal\\engine.version", "E:\\Flutter\\flutter\\bin\\internal\\engine.version", "E:\\Flutter\\flutter\\bin\\internal\\engine.version", "E:\\Flutter\\flutter\\bin\\internal\\engine.version", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\pubspec.yaml", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "E:\\Flutter\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "E:\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.16+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.15.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.0\\LICENSE", "E:\\Flutter\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "E:\\Flutter\\flutter\\packages\\flutter\\LICENSE"], "outputs": ["E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "E:\\FLUTTER_APPS\\Real-Time_Translation_App_with_Flutter_And_AI\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z"]}